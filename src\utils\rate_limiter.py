"""
Rate limiting utilities for MC-Ticketer
"""
from datetime import datetime, date
from typing import Dict
from src.config import settings


class RateLimiter:
    """Rate limiter for user ticket submissions"""
    
    def __init__(self):
        self.user_counts: Dict[int, Dict] = {}
    
    async def check_user_limit(self, user_id: int) -> bool:
        """Check if user is within rate limits"""
        today = date.today()
        
        # Initialize user data if not exists
        if user_id not in self.user_counts:
            self.user_counts[user_id] = {
                'count': 0,
                'date': today
            }
        
        user_data = self.user_counts[user_id]
        
        # Reset count if it's a new day
        if user_data['date'] != today:
            user_data['count'] = 0
            user_data['date'] = today
        
        # Check if user is within limits
        if user_data['count'] >= settings.max_tickets_per_user_per_day:
            return False
        
        # Increment count
        user_data['count'] += 1
        return True
    
    def get_user_remaining(self, user_id: int) -> int:
        """Get remaining tickets for user today"""
        today = date.today()
        
        if user_id not in self.user_counts:
            return settings.max_tickets_per_user_per_day
        
        user_data = self.user_counts[user_id]
        
        # Reset if new day
        if user_data['date'] != today:
            return settings.max_tickets_per_user_per_day
        
        return max(0, settings.max_tickets_per_user_per_day - user_data['count'])
