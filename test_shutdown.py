#!/usr/bin/env python3
"""
Test script to verify shutdown fixes
"""
import asyncio
import signal
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.bot.client import MC<PERSON><PERSON><PERSON><PERSON>ot
from src.config import settings


async def test_bot_shutdown():
    """Test bot shutdown behavior"""
    print("🤖 Testing Bot Shutdown")
    print("=" * 40)
    
    bot = None
    try:
        print("🚀 Creating bot instance...")
        bot = MCTicketerBot()
        
        print("📊 Setting up database...")
        await bot.setup_hook()
        
        print("✅ Bot setup complete")
        print("⏹️ Testing shutdown...")
        
        # Test the close method directly
        await bot.close()
        
        print("✅ Shutdown completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Shutdown test failed: {e}")
        return False
    finally:
        if bot and bot.database:
            try:
                await bot.database.close()
            except:
                pass


async def test_timeout_shutdown():
    """Test shutdown with timeout"""
    print("\n⏱️ Testing Shutdown with Timeout")
    print("=" * 40)
    
    try:
        # Test that shutdown completes within reasonable time
        await asyncio.wait_for(test_bot_shutdown(), timeout=10.0)
        print("✅ Shutdown completed within timeout")
        return True
    except asyncio.TimeoutError:
        print("❌ Shutdown timed out")
        return False
    except Exception as e:
        print(f"❌ Shutdown test failed: {e}")
        return False


async def main():
    """Run shutdown tests"""
    print("🧪 MC-Ticketer Shutdown Test")
    print("=" * 50)
    
    # Test basic shutdown
    basic_success = await test_bot_shutdown()
    
    # Test timeout shutdown
    timeout_success = await test_timeout_shutdown()
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"Basic Shutdown: {'✅ PASS' if basic_success else '❌ FAIL'}")
    print(f"Timeout Shutdown: {'✅ PASS' if timeout_success else '❌ FAIL'}")
    
    if basic_success and timeout_success:
        print("🎉 All shutdown tests passed!")
        return 0
    else:
        print("❌ Some shutdown tests failed!")
        return 1


if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(result)
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user - this is expected behavior!")
        sys.exit(0)
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        sys.exit(1)
