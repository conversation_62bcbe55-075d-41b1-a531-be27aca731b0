"""
Web scraper for Minecraft support system using Selenium-Driverless
"""
import asyncio
import random
from typing import Optional
from selenium_driverless import webdriver
from selenium_driverless.types.by import By
from loguru import logger
from src.config import settings
from src.database.models import TicketData


class MinecraftScraper:
    """Web scraper for Minecraft support ticket submission using Selenium-Driverless"""

    def __init__(self):
        self.driver: Optional[webdriver.Chrome] = None
        self.target = None

        # Lists for generating realistic random names
        self.first_names = [
            "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
            "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
            "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"
        ]

        self.last_names = [
            "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
            "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
            "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
            "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"
        ]

    async def __aenter__(self):
        """Async context manager entry"""
        await self.initialize()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.close()
    
    async def initialize(self):
        """Initialize the browser with advanced anti-detection measures using Selenium-Driverless"""
        try:
            # Configure Chrome options for maximum stealth
            options = webdriver.ChromeOptions()

            # Basic anti-detection arguments
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--disable-gpu')
            options.add_argument('--disable-features=VizDisplayCompositor')
            options.add_argument('--disable-blink-features=AutomationControlled')
            options.add_argument('--disable-extensions')
            options.add_argument('--no-first-run')
            options.add_argument('--disable-default-apps')
            options.add_argument('--disable-popup-blocking')
            options.add_argument('--disable-translate')
            options.add_argument('--disable-background-timer-throttling')
            options.add_argument('--disable-renderer-backgrounding')
            options.add_argument('--disable-backgrounding-occluded-windows')
            options.add_argument('--disable-client-side-phishing-detection')
            options.add_argument('--disable-sync')
            options.add_argument('--disable-features=TranslateUI')
            options.add_argument('--disable-ipc-flooding-protection')
            options.add_argument('--metrics-recording-only')
            options.add_argument('--no-report-upload')
            options.add_argument('--disable-web-security')
            options.add_argument('--allow-running-insecure-content')

            # Set headless mode if configured
            if settings.headless_mode:
                options.add_argument('--headless=new')

            # Set realistic window size
            width = random.randint(1366, 1920)
            height = random.randint(768, 1080)
            options.add_argument(f'--window-size={width},{height}')

            # Initialize selenium-driverless Chrome
            self.driver = webdriver.Chrome(options=options)
            await self.driver.start_session()

            # Get the current target (tab)
            self.target = await self.driver.current_target

            # Setup anti-detection measures
            await self.setup_stealth_mode()

            logger.info("Selenium-Driverless browser initialized successfully with anti-detection measures")

        except Exception as e:
            logger.error(f"Failed to initialize browser: {e}")
            raise
    
    async def setup_stealth_mode(self):
        """Setup stealth mode to avoid detection using Selenium-Driverless"""
        try:
            # Execute JavaScript to override navigator properties
            await self.target.execute_script("""
                // Override webdriver property
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                });

                // Override plugins
                Object.defineProperty(navigator, 'plugins', {
                    get: () => [
                        {
                            0: {type: "application/x-google-chrome-pdf", suffixes: "pdf", description: "Portable Document Format"},
                            description: "Portable Document Format",
                            filename: "internal-pdf-viewer",
                            length: 1,
                            name: "Chrome PDF Plugin"
                        },
                        {
                            0: {type: "application/pdf", suffixes: "pdf", description: ""},
                            description: "",
                            filename: "mhjfbmdgcfjbbpaeojofohoefgiehjai",
                            length: 1,
                            name: "Chrome PDF Viewer"
                        }
                    ],
                });

                // Override languages
                Object.defineProperty(navigator, 'languages', {
                    get: () => ['en-US', 'en'],
                });

                // Override platform
                Object.defineProperty(navigator, 'platform', {
                    get: () => 'Win32',
                });

                // Override hardwareConcurrency
                Object.defineProperty(navigator, 'hardwareConcurrency', {
                    get: () => 8,
                });

                // Override deviceMemory
                Object.defineProperty(navigator, 'deviceMemory', {
                    get: () => 8,
                });

                // Override permissions
                const originalQuery = window.navigator.permissions.query;
                window.navigator.permissions.query = (parameters) => (
                    parameters.name === 'notifications' ?
                        Promise.resolve({ state: Notification.permission }) :
                        originalQuery(parameters)
                );
            """)

            logger.info("Stealth mode configured successfully")

        except Exception as e:
            logger.error(f"Error setting up stealth mode: {e}")

    async def submit_ticket(self, ticket_data: TicketData) -> bool:
        """Submit a support ticket using Selenium-Driverless"""
        logger.info(f"Starting ticket submission for {ticket_data.ticket_id} using Selenium-Driverless")

        max_attempts = 3
        for attempt in range(max_attempts):
            try:
                logger.info(f"Submission attempt {attempt + 1}/{max_attempts}")

                # Initialize browser if not already done
                if not self.driver:
                    await self.initialize()

                # Navigate to ticket form with stealth
                await self.navigate_to_ticket_form()

                # Verify we're on the correct form
                if not await self.verify_ticket_form():
                    logger.warning("Could not verify ticket form")
                    if attempt < max_attempts - 1:
                        await self.reset_driver()
                        await asyncio.sleep(3)
                        continue
                    return False

                # Fill out the support form with human-like behavior
                form_filled = await self.fill_support_form(ticket_data)
                if not form_filled:
                    logger.warning("Failed to fill support form")
                    if attempt < max_attempts - 1:
                        await self.reset_driver()
                        await asyncio.sleep(3)
                        continue
                    return False

                # Submit the form
                submitted = await self.submit_form()
                if not submitted:
                    logger.warning("Failed to submit form")
                    if attempt < max_attempts - 1:
                        await self.reset_driver()
                        await asyncio.sleep(3)
                        continue
                    return False

                logger.info(f"Successfully submitted ticket {ticket_data.ticket_id}")
                return True

            except Exception as e:
                logger.warning(f"Submission attempt {attempt + 1} failed: {e}")
                if attempt < max_attempts - 1:
                    await self.reset_driver()
                    await asyncio.sleep(3)
                    continue
                else:
                    return False

        logger.error(f"All submission attempts failed for ticket {ticket_data.ticket_id}")
        return False

    async def reset_driver(self):
        """Reset driver instance for retry attempts"""
        try:
            logger.info("Resetting driver for retry attempt")
            await self.close()
            await asyncio.sleep(2)  # Brief pause
            await self.initialize()
        except Exception as e:
            logger.error(f"Error resetting driver: {e}")
            raise
    
    async def navigate_to_ticket_form(self):
        """Navigate to Minecraft ticket submission form with stealth measures"""
        logger.info("Navigating to Minecraft ticket submission form using Selenium-Driverless")

        # Primary target URL
        target_url = "https://help.minecraft.net/hc/en-us/requests/new"

        try:
            # Step 1: Simulate realistic browsing behavior
            await self.simulate_realistic_browsing()

            # Step 2: Navigate to main help center first (more realistic)
            logger.info("First navigating to main help center...")
            await self.target.get("https://help.minecraft.net/", wait_load=True)
            logger.info("Successfully reached main help center")

            # Random delay to appear human
            await asyncio.sleep(random.uniform(2, 4))

            # Perform some realistic interactions
            await self.random_mouse_movement()
            await self.scroll_randomly()
            await asyncio.sleep(random.uniform(1, 3))

            # Step 3: Navigate to the ticket form
            logger.info(f"Now navigating to ticket form: {target_url}")
            await self.target.get(target_url, wait_load=True)
            logger.info("Successfully navigated to ticket form")

            # Wait for page to fully load
            await asyncio.sleep(random.uniform(3, 5))

            # Check if we're on the right page
            title = await self.target.title
            current_url = await self.target.current_url
            logger.info(f"Page title: {title}")
            logger.info(f"Current URL: {current_url}")

            # Perform additional realistic behavior
            await self.random_mouse_movement()
            await asyncio.sleep(random.uniform(1, 2))

        except Exception as e:
            logger.error(f"Navigation failed: {e}")

            # Fallback: Try direct navigation
            logger.info("Trying fallback direct navigation...")
            await self.fallback_navigation(target_url)

    async def simulate_realistic_browsing(self):
        """Simulate realistic browsing patterns before accessing the target"""
        try:
            logger.info("Simulating realistic browsing behavior")

            # Visit a common website first to establish browsing history
            common_sites = [
                "https://www.google.com",
                "https://www.bing.com",
                "https://www.minecraft.net"
            ]

            site = random.choice(common_sites)
            logger.info(f"Pre-browsing to: {site}")

            try:
                await self.target.get(site, wait_load=True)
                await asyncio.sleep(random.uniform(1, 3))
                await self.random_mouse_movement()
                await self.scroll_randomly()
                await asyncio.sleep(random.uniform(0.5, 1.5))
            except Exception as e:
                logger.warning(f"Pre-browsing failed, continuing anyway: {e}")

        except Exception as e:
            logger.warning(f"Error in realistic browsing simulation: {e}")

    async def fallback_navigation(self, target_url):
        """Fallback navigation method with different strategy"""
        try:
            logger.info("Attempting fallback navigation strategy")

            # Try direct navigation to target URL with different strategies
            alternative_urls = [
                target_url,
                "https://help.minecraft.net/hc/en-us/request/new?ticket_form_id=360003469452",
                "https://help.minecraft.net/hc/en-us/request/new?ticket_form_id=4416074743565"
            ]

            for url in alternative_urls:
                try:
                    logger.info(f"Trying direct navigation to: {url}")
                    await self.target.get(url, wait_load=True)
                    logger.info(f"Fallback navigation successful to {url}")
                    await asyncio.sleep(random.uniform(2, 4))
                    return

                except Exception as e:
                    logger.warning(f"Fallback navigation failed for {url}: {e}")
                    continue

            raise Exception("All fallback navigation strategies and URLs failed")

        except Exception as e:
            logger.error(f"Fallback navigation failed: {e}")
            raise

    async def random_mouse_movement(self):
        """Perform random mouse movements to appear human"""
        try:
            # Get viewport size (use default if not available)
            viewport_width = 1366
            viewport_height = 768

            # Random coordinates within viewport
            x = random.randint(100, viewport_width - 100)
            y = random.randint(100, viewport_height - 100)

            # Move mouse to random position using pointer
            await self.target.pointer.move_to(x, y, total_time=random.uniform(0.5, 1.5))
            await asyncio.sleep(random.uniform(0.1, 0.5))

        except Exception as e:
            logger.error(f"Error with mouse movement: {e}")

    async def scroll_randomly(self):
        """Perform random scrolling to appear human"""
        try:
            # Random scroll amount and direction
            scroll_amount = random.randint(100, 500)
            direction = random.choice(['up', 'down'])

            if direction == 'down':
                await self.target.execute_script(f"window.scrollBy(0, {scroll_amount});")
            else:
                await self.target.execute_script(f"window.scrollBy(0, -{scroll_amount});")

            await asyncio.sleep(random.uniform(0.5, 1.5))

        except Exception as e:
            logger.error(f"Error with scrolling: {e}")

    async def human_like_delay(self, min_seconds: float = 0.5, max_seconds: float = 2.0):
        """Add human-like delay between actions"""
        delay = random.uniform(min_seconds, max_seconds)
        await asyncio.sleep(delay)

    def generate_random_name(self) -> str:
        """Generate a realistic random name"""
        first_name = random.choice(self.first_names)
        last_name = random.choice(self.last_names)
        return f"{first_name} {last_name}"

    async def wait_for_react_selects_ready(self):
        """Wait for React Select components to be fully loaded and ready"""
        logger.info("Waiting for React Select components to be ready...")

        try:
            # Wait for the React Select inputs to be present
            react_select_selectors = [
                '#react-select-3-input',  # Game Title
                '#react-select-4-input',  # Platform
                '#react-select-5-input'   # Category
            ]

            for selector in react_select_selectors:
                max_attempts = 10
                for attempt in range(max_attempts):
                    try:
                        elements = await self.target.find_elements(By.CSS_SELECTOR, selector, timeout=2)
                        if elements:
                            logger.info(f"✅ React Select {selector} is ready")
                            break
                    except Exception as e:
                        if attempt < max_attempts - 1:
                            logger.debug(f"Waiting for {selector} (attempt {attempt + 1}/{max_attempts})")
                            await asyncio.sleep(1)
                        else:
                            logger.warning(f"⚠️ React Select {selector} not found after {max_attempts} attempts")

            # Additional wait for React components to fully initialize
            await asyncio.sleep(3)

            # Try to trigger the dropdowns to load their options
            await self.trigger_react_select_options_loading()

            logger.info("✅ React Select components should now be ready")

        except Exception as e:
            logger.warning(f"Error waiting for React Selects: {e}")

    async def trigger_react_select_options_loading(self):
        """Trigger React Select dropdowns to load their options"""
        try:
            logger.info("Triggering React Select options loading...")

            react_selects = [
                '#react-select-3-input',  # Game Title
                '#react-select-4-input',  # Platform
                '#react-select-5-input'   # Category
            ]

            for selector in react_selects:
                try:
                    elements = await self.target.find_elements(By.CSS_SELECTOR, selector, timeout=2)
                    if elements:
                        select_input = elements[0]

                        # Click to open dropdown and trigger options loading
                        await select_input.click(move_to=True)
                        await asyncio.sleep(1)

                        # Press Escape to close without selecting
                        await select_input.send_keys('\x1b')
                        await asyncio.sleep(0.5)

                        logger.debug(f"Triggered options loading for {selector}")

                except Exception as e:
                    logger.debug(f"Could not trigger options for {selector}: {e}")
                    continue

            logger.info("✅ Finished triggering React Select options loading")

        except Exception as e:
            logger.debug(f"Error triggering React Select options: {e}")

    async def verify_ticket_form(self) -> bool:
        """Verify we're on the ticket submission form"""
        logger.info("Verifying ticket submission form")

        try:
            # Check for common form elements
            form_indicators = [
                'form',
                'input[type="email"]',
                'textarea',
                'select'
            ]

            for indicator in form_indicators:
                try:
                    elements = await self.target.find_elements(By.CSS_SELECTOR, indicator, timeout=5)
                    if elements:
                        logger.info(f"Found form element: {indicator} ({len(elements)} elements)")
                        return True
                except:
                    continue

            # Check URL to confirm we're on the right page
            current_url = await self.target.current_url
            if "request" in current_url.lower() and ("new" in current_url.lower() or "form" in current_url.lower()):
                logger.info("URL confirms we're on ticket submission page")
                return True

            logger.warning("Could not verify ticket form presence")
            return False

        except Exception as e:
            logger.error(f"Error verifying ticket form: {e}")
            return False

    async def simulate_form_inspection(self):
        """Simulate realistic form inspection behavior"""
        try:
            logger.info("Simulating form inspection behavior")

            # Random mouse movements to "look around" the form
            for _ in range(random.randint(2, 4)):
                await self.random_mouse_movement()
                await asyncio.sleep(random.uniform(0.5, 1.5))

            # Scroll to see the form better
            await self.scroll_randomly()
            await asyncio.sleep(random.uniform(1, 2))

            # More mouse movements
            await self.random_mouse_movement()
            await asyncio.sleep(random.uniform(0.5, 1))

        except Exception as e:
            logger.warning(f"Error in form inspection simulation: {e}")
    
    async def fill_support_form(self, ticket_data: TicketData) -> bool:
        """Fill out the support form with human-like behavior using Selenium-Driverless"""
        logger.info("Filling support form with stealth measures")

        try:
            # Wait for form to be fully loaded and React components to initialize
            logger.info("Waiting for form and React components to fully load...")
            await asyncio.sleep(random.uniform(5, 8))

            # Wait for React Select components to be ready
            await self.wait_for_react_selects_ready()

            # Simulate realistic user behavior before filling form
            await self.simulate_form_inspection()

            # Minecraft-specific form field selectors based on actual HTML structure
            form_fields = {
                'description': [
                    'textarea.ticket-field.ticket-field-textarea',
                    'textarea[aria-describedby*="errorMessage_360038443691"]'
                ],
                'name': [
                    'input.ticket-field.ticket-field-textbox[type="text"]:first-of-type',
                    'input[aria-describedby*="errorMessage_360038443531"]'
                ],
                'email': [
                    'input.ticket-field.ticket-field-textbox[type="text"]:nth-of-type(2)',
                    'input[aria-describedby*="errorMessage_360038443551"]'
                ],
                'confirm_email': [
                    'input.ticket-field.ticket-field-textbox[type="text"]:nth-of-type(3)',
                    'input[aria-describedby*="errorMessage_360038443571"]'
                ],
                'game_title_select': [
                    '#react-select-3-input',
                    'input[aria-label="Game Title"]'
                ],
                'platform_select': [
                    '#react-select-4-input',
                    'input[aria-label="Platform I play on"]'
                ],
                'category_select': [
                    '#react-select-5-input',
                    'input[aria-label*="Category"]'
                ]
            }

            # Create comprehensive description with all ticket information
            full_description = f"Subject: {ticket_data.subject}\n\n"
            full_description += f"Issue Type: {ticket_data.ticket_type}\n\n"
            full_description += f"Description:\n{ticket_data.description}\n\n"

            if ticket_data.minecraft_username:
                full_description += f"Minecraft Username: {ticket_data.minecraft_username}\n\n"

            full_description += "Additional Information: This ticket contains all relevant details for support assistance."

            # Map ticket type to appropriate category
            category_mapping = {
                "Account Issue": "Account",
                "Technical Issue": "Technical",
                "Billing Issue": "Billing",
                "Gameplay Issue": "Gameplay",
                "Bug Report": "Technical",
                "General Support": "General"
            }

            mapped_category = category_mapping.get(ticket_data.ticket_type, "General")

            # Fill React Select dropdowns first (required fields) with better values
            logger.info("Filling Game Title dropdown...")
            game_title_filled = await self.fill_react_select(
                form_fields['game_title_select'],
                "Minecraft Java Edition",  # More likely to match available options
                "Game Title"
            )

            await asyncio.sleep(random.uniform(2, 3))
            await self.random_mouse_movement()

            logger.info("Filling Platform dropdown...")
            platform_filled = await self.fill_react_select(
                form_fields['platform_select'],
                "Java Edition",  # More specific platform
                "Platform"
            )

            await asyncio.sleep(random.uniform(2, 3))
            await self.random_mouse_movement()

            logger.info("Filling Category dropdown...")
            category_filled = await self.fill_react_select(
                form_fields['category_select'],
                mapped_category,  # Use mapped category
                "Category"
            )

            await asyncio.sleep(random.uniform(2, 3))
            await self.random_mouse_movement()

            # Generate a realistic random name
            random_name = self.generate_random_name()
            logger.info(f"Using random name: {random_name}")

            # Fill text input fields
            name_filled = await self.fill_field_with_stealth(
                form_fields['name'],
                random_name,  # Use generated random name
                "name"
            )

            await asyncio.sleep(random.uniform(1, 2))
            await self.random_mouse_movement()

            email_filled = await self.fill_field_with_stealth(
                form_fields['email'],
                ticket_data.email,
                "email"
            )

            await asyncio.sleep(random.uniform(1, 2))
            await self.random_mouse_movement()

            confirm_email_filled = await self.fill_field_with_stealth(
                form_fields['confirm_email'],
                ticket_data.email,
                "confirm_email"
            )

            await asyncio.sleep(random.uniform(1, 2))
            await self.random_mouse_movement()

            # Fill description field (most important)
            description_filled = await self.fill_field_with_stealth(
                form_fields['description'],
                full_description,
                "description"
            )

            # Count filled fields
            required_fields = [game_title_filled, platform_filled, category_filled, name_filled, email_filled, confirm_email_filled, description_filled]
            filled_count = sum(required_fields)

            # Detailed debug logging
            logger.info("=" * 60)
            logger.info("FORM FILLING SUMMARY")
            logger.info("=" * 60)
            logger.info(f"📊 Fields filled: {filled_count}/7 required fields")
            logger.info(f"🎮 Game Title: {'✅ FILLED' if game_title_filled else '❌ FAILED'}")
            logger.info(f"💻 Platform: {'✅ FILLED' if platform_filled else '❌ FAILED'}")
            logger.info(f"📂 Category: {'✅ FILLED' if category_filled else '❌ FAILED'} (mapped from: {ticket_data.ticket_type})")
            logger.info(f"👤 Name: {'✅ FILLED' if name_filled else '❌ FAILED'} (value: {random_name})")
            logger.info(f"📧 Email: {'✅ FILLED' if email_filled else '❌ FAILED'} (value: {ticket_data.email})")
            logger.info(f"📧 Confirm Email: {'✅ FILLED' if confirm_email_filled else '❌ FAILED'}")
            logger.info(f"📝 Description: {'✅ FILLED' if description_filled else '❌ FAILED'} ({len(full_description)} chars)")
            logger.info("=" * 60)

            # Log the actual values being submitted for debugging
            logger.info("🔍 DEBUG: Form field values:")
            logger.info(f"  - Ticket Type: {ticket_data.ticket_type}")
            logger.info(f"  - Mapped Category: {mapped_category}")
            logger.info(f"  - Generated Name: {random_name}")
            logger.info(f"  - Email: {ticket_data.email}")
            logger.info(f"  - Subject: {ticket_data.subject}")
            logger.info(f"  - Description length: {len(full_description)} characters")

            # Need at least 6 out of 7 fields filled to proceed (all except maybe one)
            if filled_count >= 6:
                logger.info("✅ Form filling completed successfully (sufficient required fields filled)")

                # Take a screenshot before submission for debugging
                try:
                    await self.target.save_screenshot("form_before_submission.png")
                    logger.info("📸 Screenshot saved: form_before_submission.png")
                except Exception as e:
                    logger.debug(f"Could not save pre-submission screenshot: {e}")

                return True
            else:
                logger.error(f"❌ Failed to fill minimum required fields ({filled_count}/7)")
                logger.error("🚨 Cannot proceed with submission - too many fields failed")

                # Take a screenshot for debugging failed form filling
                try:
                    await self.target.save_screenshot("form_filling_failed.png")
                    logger.info("📸 Debug screenshot saved: form_filling_failed.png")
                except Exception as e:
                    logger.debug(f"Could not save debug screenshot: {e}")

                return False
            
        except Exception as e:
            logger.error(f"Error filling form: {e}")
            return False

    async def fill_field_with_stealth(self, selectors, value, field_name):
        """Fill a form field with advanced stealth measures using Selenium-Driverless"""
        try:
            for selector in selectors:
                try:
                    # Wait for field with random timeout
                    timeout = random.randint(3, 7)
                    elements = await self.target.find_elements(By.CSS_SELECTOR, selector, timeout=timeout)

                    if elements:
                        field = elements[0]  # Use first found element

                        # Simulate realistic field interaction
                        await self.simulate_field_focus(field)

                        # Clear field first (realistic behavior)
                        await field.click()
                        await asyncio.sleep(random.uniform(0.2, 0.5))
                        await field.clear()
                        await asyncio.sleep(random.uniform(0.1, 0.3))

                        # Type with realistic human patterns
                        await self.type_like_human(field, value)

                        logger.info(f"Successfully filled {field_name} field using selector: {selector}")

                        # Random delay after filling
                        await asyncio.sleep(random.uniform(0.5, 1.5))
                        return True

                except Exception as e:
                    logger.debug(f"{field_name} selector {selector} failed: {e}")
                    continue

            logger.warning(f"Could not find {field_name} field")
            return False

        except Exception as e:
            logger.error(f"Error filling {field_name} field: {e}")
            return False

    async def simulate_field_focus(self, field):
        """Simulate realistic field focusing behavior"""
        try:
            # Move to field and click
            await field.click(move_to=True)
            await asyncio.sleep(random.uniform(0.2, 0.5))

        except Exception as e:
            logger.debug(f"Error in field focus simulation: {e}")

    async def type_like_human(self, field, text):
        """Type text with realistic human patterns"""
        try:
            # Use the write method with selenium-driverless for more reliable typing
            await field.write(text)

            # Add some realistic delays to simulate human typing
            await asyncio.sleep(random.uniform(0.5, 1.5))

        except Exception as e:
            logger.error(f"Error in human-like typing: {e}")
            # Fallback to send_keys if write fails
            try:
                await field.send_keys(text)
            except Exception as e2:
                logger.error(f"Fallback typing also failed: {e2}")
                raise

    async def fill_react_select(self, selectors, value, field_name):
        """Fill a React Select dropdown component with enhanced option detection"""
        try:
            for selector in selectors:
                try:
                    # Find the React Select input
                    elements = await self.target.find_elements(By.CSS_SELECTOR, selector, timeout=5)
                    if elements:
                        select_input = elements[0]

                        logger.info(f"Found React Select input for {field_name}: {selector}")

                        # Click on the input to open dropdown
                        await select_input.click(move_to=True)
                        await asyncio.sleep(random.uniform(1.0, 2.0))  # Longer wait for dropdown to appear

                        # Clear any existing text first
                        await select_input.clear()
                        await asyncio.sleep(random.uniform(0.3, 0.5))

                        # Type the value to search/filter options (slower for better matching)
                        for char in value:
                            await select_input.send_keys(char)
                            await asyncio.sleep(random.uniform(0.1, 0.2))  # Slower typing

                        # Wait longer for options to filter/appear
                        await asyncio.sleep(random.uniform(1.0, 2.0))

                        # Try to select the first matching option
                        success = await self.select_dropdown_option(field_name, value)
                        if success:
                            return True

                        # If that didn't work, try alternative approach
                        logger.info(f"Trying alternative selection method for {field_name}")

                        # Clear and try again with just the first few characters
                        await select_input.clear()
                        await asyncio.sleep(0.5)

                        # Type just the first word or few characters
                        search_term = value.split()[0] if ' ' in value else value[:5]
                        for char in search_term:
                            await select_input.send_keys(char)
                            await asyncio.sleep(random.uniform(0.1, 0.2))

                        await asyncio.sleep(1.5)

                        # Try to select again
                        success = await self.select_dropdown_option(field_name, search_term)
                        if success:
                            return True

                        # Last resort: press Enter to select whatever is highlighted
                        await select_input.send_keys('\n')
                        await asyncio.sleep(random.uniform(0.5, 1.0))

                        # Verify selection was made
                        if await self.verify_react_select_selection(selector, field_name):
                            logger.info(f"Successfully selected {field_name} using Enter key")
                            return True

                        # If still no success, try pressing Escape and continue to next selector
                        try:
                            await select_input.send_keys('\x1b')  # Escape key
                            await asyncio.sleep(0.5)
                        except:
                            pass

                except Exception as e:
                    logger.debug(f"React Select selector {selector} failed for {field_name}: {e}")
                    continue

            logger.warning(f"Could not successfully select option for {field_name}")
            return False

        except Exception as e:
            logger.error(f"Error filling React Select {field_name}: {e}")
            return False

    async def select_dropdown_option(self, field_name, search_value):
        """Try to select an option from the dropdown menu"""
        try:
            # Enhanced option selectors for React Select
            option_selectors = [
                '[class*="option"]:not([class*="placeholder"])',
                '[role="option"]',
                '[class*="menu"] > div > div',
                '[class*="select__option"]',
                '[id*="option-"]',
                'div[class*="option"][tabindex]'
            ]

            for option_selector in option_selectors:
                try:
                    # Wait for options to appear
                    options = await self.target.find_elements(By.CSS_SELECTOR, option_selector, timeout=3)
                    if options:
                        logger.info(f"Found {len(options)} options for {field_name}")

                        # Try to find an option that contains our search value
                        for option in options:
                            try:
                                option_text = await option.get_attribute('textContent')
                                if option_text and (search_value.lower() in option_text.lower() or
                                                  option_text.lower() in search_value.lower()):
                                    logger.info(f"Clicking option '{option_text}' for {field_name}")
                                    await option.click(move_to=True)
                                    await asyncio.sleep(random.uniform(0.5, 1.0))
                                    return True
                            except Exception as e:
                                logger.debug(f"Error checking option text: {e}")
                                continue

                        # If no matching option found, click the first visible option
                        if options:
                            try:
                                first_option = options[0]
                                option_text = await first_option.get_attribute('textContent')
                                logger.info(f"Clicking first available option '{option_text}' for {field_name}")
                                await first_option.click(move_to=True)
                                await asyncio.sleep(random.uniform(0.5, 1.0))
                                return True
                            except Exception as e:
                                logger.debug(f"Error clicking first option: {e}")

                except Exception as e:
                    logger.debug(f"Option selector {option_selector} failed: {e}")
                    continue

            return False

        except Exception as e:
            logger.error(f"Error selecting dropdown option for {field_name}: {e}")
            return False

    async def verify_react_select_selection(self, selector, field_name):
        """Verify that a React Select has a value selected"""
        try:
            # Check if the select now has a value (not placeholder)
            elements = await self.target.find_elements(By.CSS_SELECTOR, selector, timeout=3)
            if elements:
                select_input = elements[0]

                # Check the parent container for selected value
                parent_selectors = [
                    'div[class*="single-value"]',
                    'div[class*="value-container"] div:not([class*="placeholder"])',
                    'div[class*="control"] div[class*="single-value"]'
                ]

                for parent_selector in parent_selectors:
                    try:
                        # Look for selected value in the parent container using target instead of element
                        parent_elements = await self.target.find_elements(By.XPATH, f"//input[@id='{selector.replace('#', '')}']/..", timeout=2)
                        if parent_elements:
                            parent = parent_elements[0]
                            value_elements = await self.target.find_elements(By.CSS_SELECTOR, f"{parent_selector}", timeout=2)
                            for value_element in value_elements:
                                value_text = await value_element.get_attribute('textContent')
                                if value_text and value_text.strip() and 'Please Select' not in value_text and 'No options' not in value_text:
                                    logger.info(f"Verified {field_name} selection: '{value_text}'")
                                    return True
                    except Exception as e:
                        logger.debug(f"Error checking parent selector {parent_selector}: {e}")
                        continue

            # Alternative check: look for any non-placeholder text in the React Select container
            try:
                container_selector = selector.replace('-input', '')
                container_elements = await self.target.find_elements(By.CSS_SELECTOR, f"#{container_selector.replace('#', '')}", timeout=2)
                if container_elements:
                    container_text = await container_elements[0].get_attribute('textContent')
                    if container_text and 'Please Select' not in container_text and 'No options' not in container_text:
                        logger.info(f"Alternative verification for {field_name}: found content")
                        return True
            except Exception as e:
                logger.debug(f"Alternative verification failed: {e}")

            return False

        except Exception as e:
            logger.debug(f"Error verifying React Select selection for {field_name}: {e}")
            return False

    async def submit_form(self) -> bool:
        """Submit the support form with enhanced validation and verification"""
        logger.info("Preparing to submit form...")

        try:
            # First, validate that all required fields are properly filled
            validation_passed = await self.validate_form_before_submission()
            if not validation_passed:
                logger.error("Form validation failed - cannot submit")
                return False

            # Scroll to submit button area first
            await self.target.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            await asyncio.sleep(random.uniform(2, 3))

            # Look for submit button with specific Minecraft form selectors
            submit_selectors = [
                'button.button.button-submit.button-green',
                'button[data-aem-contentname="submit"]',
                'button[type="submit"]',
                '.button-submit'
            ]

            submit_button = None
            for selector in submit_selectors:
                try:
                    elements = await self.target.find_elements(By.CSS_SELECTOR, selector, timeout=5)
                    if elements:
                        submit_button = elements[0]
                        logger.info(f"Found submit button with selector: {selector}")
                        break
                except Exception as e:
                    logger.debug(f"Submit selector {selector} failed: {e}")
                    continue

            if not submit_button:
                logger.error("Could not find submit button")
                return False

            # Check if submit button is enabled
            is_disabled = await submit_button.get_attribute('disabled')
            if is_disabled:
                logger.error("Submit button is disabled - form validation may have failed")
                return False

            # Ensure button is visible and clickable
            await self.target.execute_script("arguments[0].scrollIntoView(true);", submit_button)
            await asyncio.sleep(random.uniform(1, 2))

            # Human-like interaction before clicking
            await self.random_mouse_movement()
            await asyncio.sleep(random.uniform(1, 2))

            # Get initial URL and page state for comparison
            initial_url = await self.target.current_url
            logger.info(f"Initial URL before submission: {initial_url}")

            # Click the submit button
            logger.info("Clicking submit button...")
            await submit_button.click(move_to=True)

            # Wait for initial processing
            await asyncio.sleep(3)

            # Check for immediate validation errors
            validation_errors = await self.check_for_validation_errors()
            if validation_errors:
                logger.error(f"Form validation errors found: {validation_errors}")
                return False

            # Wait longer for submission to complete and page to redirect
            logger.info("Waiting for form submission to complete...")
            await asyncio.sleep(10)  # Longer wait for processing

            # Check for success indicators
            success = await self.verify_submission_success(initial_url)

            if success:
                logger.info("✅ Form submission verified as successful!")
                return True
            else:
                logger.warning("⚠️ Could not verify successful form submission")

                # Take a screenshot for debugging
                try:
                    await self.target.save_screenshot("form_submission_debug.png")
                    logger.info("📸 Debug screenshot saved as form_submission_debug.png")
                except Exception as e:
                    logger.debug(f"Could not save debug screenshot: {e}")

                return False

        except Exception as e:
            logger.error(f"Error during form submission: {e}")
            return False

    async def validate_form_before_submission(self) -> bool:
        """Validate that all required fields are properly filled before submission"""
        logger.info("Validating form fields before submission...")

        try:
            # Check React Select fields have values (not placeholders)
            react_selects = [
                ('#react-select-3-input', 'Game Title'),
                ('#react-select-4-input', 'Platform'),
                ('#react-select-5-input', 'Category')
            ]

            for selector, field_name in react_selects:
                try:
                    elements = await self.target.find_elements(By.CSS_SELECTOR, selector, timeout=3)
                    if elements:
                        # Check if the select has a selected value
                        has_value = await self.verify_react_select_selection(selector, field_name)
                        if not has_value:
                            logger.error(f"❌ {field_name} field is not properly selected")
                            return False
                        else:
                            logger.info(f"✅ {field_name} field is properly selected")
                except Exception as e:
                    logger.error(f"Error validating {field_name}: {e}")
                    return False

            # Check text input fields have values
            text_inputs = [
                ('input[aria-describedby*="errorMessage_360038443531"]', 'Name'),
                ('input[aria-describedby*="errorMessage_360038443551"]', 'Email'),
                ('input[aria-describedby*="errorMessage_360038443571"]', 'Confirm Email'),
                ('textarea[aria-describedby*="errorMessage_360038443691"]', 'Description')
            ]

            for selector, field_name in text_inputs:
                try:
                    elements = await self.target.find_elements(By.CSS_SELECTOR, selector, timeout=3)
                    if elements:
                        field_value = await elements[0].get_attribute('value')
                        if not field_value or len(field_value.strip()) < 3:
                            logger.error(f"❌ {field_name} field is empty or too short")
                            return False
                        else:
                            logger.info(f"✅ {field_name} field has value: {field_value[:50]}...")
                except Exception as e:
                    logger.error(f"Error validating {field_name}: {e}")
                    return False

            logger.info("✅ All form fields validation passed")
            return True

        except Exception as e:
            logger.error(f"Error during form validation: {e}")
            return False

    async def check_for_validation_errors(self) -> list:
        """Check for form validation error messages"""
        try:
            error_selectors = [
                '[class*="error"]',
                '[class*="invalid"]',
                '[aria-invalid="true"]',
                '.field-error',
                '.validation-error',
                '[role="alert"]'
            ]

            errors = []
            for selector in error_selectors:
                try:
                    error_elements = await self.target.find_elements(By.CSS_SELECTOR, selector, timeout=2)
                    for element in error_elements:
                        error_text = await element.get_attribute('textContent')
                        if error_text and error_text.strip():
                            errors.append(error_text.strip())
                except Exception as e:
                    logger.debug(f"Error checking selector {selector}: {e}")
                    continue

            return errors

        except Exception as e:
            logger.debug(f"Error checking for validation errors: {e}")
            return []

    async def verify_submission_success(self, initial_url: str) -> bool:
        """Verify that form submission was successful"""
        try:
            # Check current URL for changes
            current_url = await self.target.current_url
            logger.info(f"Current URL after submission: {current_url}")

            # Check for URL changes indicating redirect
            if current_url != initial_url:
                logger.info("✅ URL changed after submission - likely successful")

                # Check for success indicators in new URL
                success_url_indicators = [
                    'thank', 'success', 'confirmation', 'submitted', 'complete'
                ]

                for indicator in success_url_indicators:
                    if indicator in current_url.lower():
                        logger.info(f"✅ Found success URL indicator: {indicator}")
                        return True

            # Check page content for success messages
            try:
                page_content = await self.target.execute_script("return document.body.innerText.toLowerCase();")
                success_text_indicators = [
                    'thank you for contacting',
                    'your request has been submitted',
                    'we have received your request',
                    'ticket has been created',
                    'confirmation number',
                    'request submitted successfully'
                ]

                for indicator in success_text_indicators:
                    if indicator in page_content:
                        logger.info(f"✅ Found success text indicator: {indicator}")
                        return True

                # Log a sample of page content for debugging
                content_sample = page_content[:500] if page_content else "No content"
                logger.info(f"Page content sample: {content_sample}")

            except Exception as e:
                logger.debug(f"Error checking page content: {e}")

            # Check page title for success indicators
            try:
                page_title = await self.target.title
                logger.info(f"Page title after submission: {page_title}")

                success_title_words = ['thank', 'success', 'submitted', 'confirmation']
                if any(word in page_title.lower() for word in success_title_words):
                    logger.info(f"✅ Found success indicator in page title")
                    return True

            except Exception as e:
                logger.debug(f"Error checking page title: {e}")

            # Check for absence of the original form (indicating successful submission)
            try:
                form_elements = await self.target.find_elements(By.CSS_SELECTOR, 'button.button-submit', timeout=3)
                if not form_elements:
                    logger.info("✅ Original form no longer present - likely successful submission")
                    return True
            except Exception as e:
                logger.debug(f"Error checking for form presence: {e}")

            logger.warning("❌ No clear success indicators found")
            return False

        except Exception as e:
            logger.error(f"Error verifying submission success: {e}")
            return False
            
        except Exception as e:
            logger.error(f"Error submitting form: {e}")
            return False
    
    async def close(self):
        """Close browser and cleanup"""
        try:
            if self.driver:
                await self.driver.quit()
                self.driver = None
                self.target = None

            logger.info("Selenium-Driverless browser closed successfully")

        except Exception as e:
            logger.error(f"Error closing browser: {e}")


