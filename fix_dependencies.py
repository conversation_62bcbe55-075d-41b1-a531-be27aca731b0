#!/usr/bin/env python3
"""
Quick fix script for dependency issues
"""
import subprocess
import sys


def run_command(command):
    """Run a command and return success status"""
    try:
        result = subprocess.run(command, check=True, capture_output=True, text=True, shell=True)
        print(f"✅ {' '.join(command) if isinstance(command, list) else command}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {' '.join(command) if isinstance(command, list) else command}")
        print(f"Error: {e.stderr}")
        return False


def main():
    print("🔧 MC-Ticketer Dependency Fix")
    print("=" * 40)
    
    # Install/upgrade required packages
    commands = [
        "pip install --upgrade pydantic-settings",
        "pip install --upgrade pydantic>=2.5.0",
        "pip install --upgrade discord.py>=2.3.0",
        "pip install --upgrade playwright>=1.40.0",
        "pip install --upgrade python-dotenv>=1.0.0",
        "pip install --upgrade loguru>=0.7.0",
        "pip install --upgrade aiosqlite>=0.19.0"
    ]
    
    success_count = 0
    for cmd in commands:
        if run_command(cmd):
            success_count += 1
    
    print(f"\n📊 {success_count}/{len(commands)} packages installed successfully")
    
    # Install Playwright browsers
    print("\n🌐 Installing Playwright browsers...")
    if run_command("playwright install"):
        print("✅ Playwright browsers installed")
    else:
        print("❌ Failed to install Playwright browsers")
    
    # Test imports
    print("\n🧪 Testing imports...")
    test_imports = [
        "import discord",
        "import playwright",
        "import pydantic",
        "from pydantic_settings import BaseSettings",
        "import loguru",
        "import aiosqlite"
    ]
    
    for test_import in test_imports:
        try:
            exec(test_import)
            print(f"✅ {test_import}")
        except ImportError as e:
            print(f"❌ {test_import} - {e}")
    
    print("\n🎉 Dependency fix complete!")
    print("Try running the bot again: python main.py")


if __name__ == "__main__":
    main()
