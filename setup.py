"""
Setup script for MC-Ticketer
"""
import asyncio
import os
import sys
from pathlib import Path


async def setup_environment():
    """Setup the development environment"""
    print("🎫 MC-Ticketer Setup")
    print("=" * 50)
    
    # Check Python version
    if sys.version_info < (3, 9):
        print("❌ Python 3.9 or higher is required")
        return False
    
    print("✅ Python version check passed")
    
    # Check if .env exists
    env_file = Path(".env")
    if not env_file.exists():
        print("📝 Creating .env file from template...")
        env_example = Path(".env.example")
        if env_example.exists():
            env_file.write_text(env_example.read_text())
            print("✅ .env file created")
            print("⚠️  Please edit .env with your Discord bot token and other settings")
        else:
            print("❌ .env.example not found")
            return False
    else:
        print("✅ .env file already exists")
    
    # Create directories
    directories = ["data", "logs"]
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ Created directory: {directory}")
    
    print("\n🚀 Setup complete!")
    print("\nNext steps:")
    print("1. Edit .env file with your Discord bot token")
    print("2. Install dependencies: pip install -r requirements.txt")
    print("3. Install Playwright browsers: playwright install")
    print("4. Run the bot: python main.py")
    
    return True


if __name__ == "__main__":
    asyncio.run(setup_environment())
