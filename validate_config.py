#!/usr/bin/env python3
"""
Configuration validation script for MC-Ticketer
"""
import sys
import os
from pathlib import Path


def check_python_version():
    """Check Python version"""
    if sys.version_info < (3, 9):
        print("❌ Python 3.9+ required")
        return False
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    return True


def check_env_file():
    """Check if .env file exists and has required variables"""
    env_file = Path(".env")
    if not env_file.exists():
        print("❌ .env file not found")
        print("   Run: cp .env.example .env")
        return False
    
    print("✅ .env file found")
    
    # Check required variables
    required_vars = [
        "DISCORD_BOT_TOKEN",
        "COMMAND_PREFIX",
        "LOG_LEVEL"
    ]
    
    missing_vars = []
    with open(env_file) as f:
        content = f.read()
        for var in required_vars:
            if f"{var}=" not in content or f"{var}=your_" in content:
                missing_vars.append(var)
    
    if missing_vars:
        print(f"⚠️  Missing or unconfigured variables: {', '.join(missing_vars)}")
        return False
    
    print("✅ Required environment variables configured")
    return True


def check_dependencies():
    """Check if required dependencies are installed"""
    required_packages = [
        ("discord.py", "discord"),
        ("playwright", "playwright"),
        ("pydantic", "pydantic"),
        ("loguru", "loguru"),
        ("aiosqlite", "aiosqlite")
    ]
    
    missing_packages = []
    for package_name, import_name in required_packages:
        try:
            __import__(import_name)
            print(f"✅ {package_name}")
        except ImportError:
            print(f"❌ {package_name}")
            missing_packages.append(package_name)
    
    if missing_packages:
        print(f"\nInstall missing packages:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True


def check_playwright_browsers():
    """Check if Playwright browsers are installed"""
    try:
        from playwright.sync_api import sync_playwright
        with sync_playwright() as p:
            browser = p.chromium.launch(headless=True)
            browser.close()
        print("✅ Playwright browsers installed")
        return True
    except Exception as e:
        print(f"❌ Playwright browsers not installed: {e}")
        print("   Run: playwright install")
        return False


def check_directories():
    """Check if required directories exist"""
    required_dirs = ["data", "logs", "src"]
    
    for directory in required_dirs:
        dir_path = Path(directory)
        if dir_path.exists():
            print(f"✅ {directory}/ directory")
        else:
            print(f"❌ {directory}/ directory missing")
            dir_path.mkdir(exist_ok=True)
            print(f"   Created {directory}/ directory")
    
    return True


def check_config_import():
    """Check if configuration can be imported"""
    try:
        sys.path.insert(0, "src")
        from src.config import settings
        print("✅ Configuration import successful")
        print(f"   Command prefix: {settings.command_prefix}")
        print(f"   Log level: {settings.log_level}")
        return True
    except Exception as e:
        print(f"❌ Configuration import failed: {e}")
        return False


def check_database_creation():
    """Check if database can be created"""
    try:
        import asyncio
        sys.path.insert(0, "src")
        from src.database.models import Database
        
        async def test_db():
            db = Database()
            await db.initialize()
            await db.close()
            return True
        
        result = asyncio.run(test_db())
        print("✅ Database creation test passed")
        return result
    except Exception as e:
        print(f"❌ Database creation test failed: {e}")
        return False


def main():
    """Main validation function"""
    print("🎫 MC-Ticketer Configuration Validation")
    print("=" * 50)
    
    checks = [
        ("Python Version", check_python_version),
        ("Environment File", check_env_file),
        ("Dependencies", check_dependencies),
        ("Playwright Browsers", check_playwright_browsers),
        ("Directories", check_directories),
        ("Configuration Import", check_config_import),
        ("Database Creation", check_database_creation)
    ]
    
    passed = 0
    total = len(checks)
    
    for check_name, check_func in checks:
        print(f"\n🔍 Checking {check_name}...")
        try:
            if check_func():
                passed += 1
        except Exception as e:
            print(f"❌ {check_name} check failed with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Validation Summary: {passed}/{total} checks passed")
    
    if passed == total:
        print("🎉 All checks passed! MC-Ticketer is ready to run.")
        print("\nTo start the bot:")
        print("  python main.py")
        return 0
    else:
        print("❌ Some checks failed. Please fix the issues above.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
