# MC-Ticketer Deployment Guide

## Overview

This guide covers deploying MC-Ticketer in various environments, from development to production.

## Prerequisites

- Python 3.9+
- Discord Bo<PERSON> Token
- Server with internet access
- Sufficient permissions to install software

## Local Development Deployment

### Quick Start

1. **Clone and Setup**:
   ```bash
   git clone <repository-url>
   cd MC-Ticketer
   python setup.py
   ```

2. **Install Dependencies**:
   ```bash
   pip install -r requirements.txt
   playwright install
   ```

3. **Configure Environment**:
   ```bash
   cp .env.example .env
   # Edit .env with your settings
   ```

4. **Run the Bot**:
   ```bash
   python main.py
   ```

## Production Deployment

### Option 1: Direct Server Deployment

#### System Requirements

- **OS**: Ubuntu 20.04+ / CentOS 8+ / Windows Server 2019+
- **RAM**: Minimum 1GB, Recommended 2GB+
- **Storage**: 5GB+ free space
- **Network**: Stable internet connection

#### Installation Steps

1. **Prepare the Server**:
   ```bash
   # Ubuntu/Debian
   sudo apt update
   sudo apt install python3.9 python3.9-venv python3-pip git
   
   # CentOS/RHEL
   sudo yum install python39 python39-pip git
   ```

2. **Create Application User**:
   ```bash
   sudo useradd -m -s /bin/bash mcticket
   sudo su - mcticket
   ```

3. **Deploy Application**:
   ```bash
   git clone <repository-url> mc-ticketer
   cd mc-ticketer
   python3.9 -m venv venv
   source venv/bin/activate
   pip install -r requirements.txt
   playwright install
   ```

4. **Configure Environment**:
   ```bash
   cp .env.example .env
   nano .env  # Edit with production settings
   ```

5. **Create Systemd Service** (Linux):
   ```bash
   sudo nano /etc/systemd/system/mc-ticketer.service
   ```
   
   ```ini
   [Unit]
   Description=MC-Ticketer Discord Bot
   After=network.target
   
   [Service]
   Type=simple
   User=mcticket
   WorkingDirectory=/home/<USER>/mc-ticketer
   Environment=PATH=/home/<USER>/mc-ticketer/venv/bin
   ExecStart=/home/<USER>/mc-ticketer/venv/bin/python main.py
   Restart=always
   RestartSec=10
   
   [Install]
   WantedBy=multi-user.target
   ```

6. **Start and Enable Service**:
   ```bash
   sudo systemctl daemon-reload
   sudo systemctl enable mc-ticketer
   sudo systemctl start mc-ticketer
   sudo systemctl status mc-ticketer
   ```

### Option 2: Docker Deployment

#### Dockerfile

Create `Dockerfile`:
```dockerfile
FROM python:3.9-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    wget \
    gnupg \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Install Playwright browsers
RUN playwright install --with-deps

# Copy application code
COPY . .

# Create non-root user
RUN useradd -m -u 1000 mcticket && chown -R mcticket:mcticket /app
USER mcticket

# Create necessary directories
RUN mkdir -p data logs

# Expose port (if needed for health checks)
EXPOSE 8080

# Run the application
CMD ["python", "main.py"]
```

#### Docker Compose

Create `docker-compose.yml`:
```yaml
version: '3.8'

services:
  mc-ticketer:
    build: .
    container_name: mc-ticketer
    restart: unless-stopped
    environment:
      - DISCORD_BOT_TOKEN=${DISCORD_BOT_TOKEN}
      - DISCORD_GUILD_ID=${DISCORD_GUILD_ID}
      - COMMAND_PREFIX=${COMMAND_PREFIX}
      - ADMIN_ROLE_ID=${ADMIN_ROLE_ID}
      - HEADLESS_MODE=true
      - LOG_LEVEL=INFO
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    env_file:
      - .env
    healthcheck:
      test: ["CMD", "python", "-c", "import sqlite3; sqlite3.connect('/app/data/tickets.db').close()"]
      interval: 30s
      timeout: 10s
      retries: 3
```

#### Deploy with Docker

```bash
# Build and run
docker-compose up -d

# View logs
docker-compose logs -f

# Stop
docker-compose down
```

### Option 3: Cloud Deployment

#### Heroku Deployment

1. **Prepare for Heroku**:
   ```bash
   # Install Heroku CLI
   # Create Procfile
   echo "worker: python main.py" > Procfile
   
   # Create runtime.txt
   echo "python-3.9.16" > runtime.txt
   ```

2. **Deploy to Heroku**:
   ```bash
   heroku create mc-ticketer-app
   heroku config:set DISCORD_BOT_TOKEN=your_token_here
   heroku config:set HEADLESS_MODE=true
   git push heroku main
   heroku ps:scale worker=1
   ```

#### AWS EC2 Deployment

1. **Launch EC2 Instance**:
   - Choose Ubuntu 20.04 LTS
   - t3.small or larger recommended
   - Configure security groups (SSH access)

2. **Connect and Deploy**:
   ```bash
   ssh -i your-key.pem ubuntu@your-ec2-ip
   # Follow "Direct Server Deployment" steps
   ```

#### DigitalOcean Droplet

1. **Create Droplet**:
   - Ubuntu 20.04
   - $10/month droplet recommended
   - Add SSH key

2. **Deploy Application**:
   ```bash
   ssh root@your-droplet-ip
   # Follow "Direct Server Deployment" steps
   ```

## Configuration for Production

### Environment Variables

```env
# Discord Configuration
DISCORD_BOT_TOKEN=your_production_bot_token
DISCORD_GUILD_ID=your_server_id
COMMAND_PREFIX=!
ADMIN_ROLE_ID=your_admin_role_id

# Production Settings
HEADLESS_MODE=true
LOG_LEVEL=INFO
MAX_TICKETS_PER_USER_PER_DAY=5
RATE_LIMIT_WINDOW_MINUTES=60

# Browser Settings
BROWSER_TIMEOUT=45000
USER_AGENT=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36

# Database
DATABASE_PATH=data/tickets.db
LOG_FILE=logs/mc_ticketer.log
```

### Security Considerations

1. **Bot Token Security**:
   - Use environment variables
   - Never commit tokens to version control
   - Rotate tokens regularly
   - Restrict bot permissions to minimum required

2. **Server Security**:
   - Keep system updated
   - Use firewall (UFW on Ubuntu)
   - Disable root SSH login
   - Use SSH keys instead of passwords

3. **Application Security**:
   - Run as non-root user
   - Set proper file permissions
   - Monitor logs for suspicious activity
   - Implement rate limiting

### Monitoring and Logging

#### Log Management

1. **Log Rotation**:
   ```bash
   sudo nano /etc/logrotate.d/mc-ticketer
   ```
   
   ```
   /home/<USER>/mc-ticketer/logs/*.log {
       daily
       missingok
       rotate 30
       compress
       delaycompress
       notifempty
       copytruncate
   }
   ```

2. **Centralized Logging** (Optional):
   - ELK Stack (Elasticsearch, Logstash, Kibana)
   - Fluentd
   - Grafana Loki

#### Health Monitoring

1. **System Monitoring**:
   ```bash
   # Check service status
   sudo systemctl status mc-ticketer
   
   # View recent logs
   sudo journalctl -u mc-ticketer -f
   
   # Check resource usage
   htop
   ```

2. **Application Monitoring**:
   - Monitor database size
   - Track error rates
   - Monitor response times
   - Set up alerts for failures

### Backup and Recovery

#### Database Backup

```bash
#!/bin/bash
# backup.sh
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/home/<USER>/backups"
DB_PATH="/home/<USER>/mc-ticketer/data/tickets.db"

mkdir -p $BACKUP_DIR
cp $DB_PATH $BACKUP_DIR/tickets_$DATE.db
gzip $BACKUP_DIR/tickets_$DATE.db

# Keep only last 30 days
find $BACKUP_DIR -name "tickets_*.db.gz" -mtime +30 -delete
```

#### Automated Backups

```bash
# Add to crontab
crontab -e

# Backup daily at 2 AM
0 2 * * * /home/<USER>/backup.sh
```

### Performance Optimization

1. **Database Optimization**:
   - Regular VACUUM operations
   - Index optimization
   - Monitor database size

2. **Memory Management**:
   - Monitor memory usage
   - Implement connection pooling
   - Optimize browser instances

3. **Network Optimization**:
   - Use CDN for static assets (if any)
   - Implement caching
   - Optimize API calls

### Troubleshooting

#### Common Issues

1. **Bot Not Starting**:
   ```bash
   # Check logs
   sudo journalctl -u mc-ticketer -n 50
   
   # Check configuration
   cd /home/<USER>/mc-ticketer
   source venv/bin/activate
   python -c "from src.config import settings; print('Config loaded')"
   ```

2. **Web Scraping Failures**:
   ```bash
   # Check browser installation
   playwright install --with-deps
   
   # Test browser
   python -c "from playwright.sync_api import sync_playwright; print('Playwright OK')"
   ```

3. **Database Issues**:
   ```bash
   # Check database file
   ls -la data/tickets.db
   
   # Test database connection
   sqlite3 data/tickets.db ".tables"
   ```

#### Emergency Recovery

1. **Service Recovery**:
   ```bash
   sudo systemctl stop mc-ticketer
   sudo systemctl start mc-ticketer
   ```

2. **Database Recovery**:
   ```bash
   # Restore from backup
   cp /home/<USER>/backups/tickets_YYYYMMDD_HHMMSS.db.gz .
   gunzip tickets_YYYYMMDD_HHMMSS.db.gz
   mv tickets_YYYYMMDD_HHMMSS.db data/tickets.db
   ```

### Scaling Considerations

1. **Horizontal Scaling**:
   - Multiple bot instances (different servers)
   - Load balancing
   - Shared database

2. **Vertical Scaling**:
   - Increase server resources
   - Optimize code performance
   - Database optimization

3. **High Availability**:
   - Multiple server deployment
   - Database replication
   - Health checks and failover
