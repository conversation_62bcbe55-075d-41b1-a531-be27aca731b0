# Discord Bot Configuration
DISCORD_BOT_TOKEN=your_discord_bot_token_here
DISCORD_GUILD_ID=your_discord_server_id_here

# Bot Settings
COMMAND_PREFIX=!
ADMIN_ROLE_ID=your_admin_role_id_here

# Web Scraping Configuration
USER_AGENT=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36
HEADLESS_MODE=true
BROWSER_TIMEOUT=30000

# Rate Limiting
MAX_TICKETS_PER_USER_PER_DAY=3
RATE_LIMIT_WINDOW_MINUTES=60

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/mc_ticketer.log

# Database
DATABASE_PATH=data/tickets.db
