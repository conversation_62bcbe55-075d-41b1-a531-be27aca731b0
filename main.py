"""
MC-Ticketer - Automated Minecraft Support Ticket Generator
Main entry point for the application
"""
import asyncio
import sys
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.config import settings
from src.utils.logger import setup_logger
from src.bot.client import MCTicketerBot


async def main():
    """Main application entry point"""
    # Setup logging
    logger = setup_logger()
    logger.info("Starting MC-Ticketer...")

    bot = None
    try:
        # Initialize and run the Discord bot
        bot = MCTicketerBot()
        await bot.start(settings.discord_bot_token)
    except KeyboardInterrupt:
        logger.info("Received shutdown signal (Ctrl+C)")
        if bot:
            logger.info("Initiating graceful shutdown...")
            await bot.close()
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        if bot:
            await bot.close()
        raise
    finally:
        logger.info("MC-Ticketer shutdown complete")


if __name__ == "__main__":
    asyncio.run(main())
