"""
Ticket-related Discord commands
"""
import discord
from discord.ext import commands
from loguru import logger
from src.bot.views.ticket_modal import TicketModal
from src.database.models import TicketStatus
from src.utils.rate_limiter import RateLimiter


class TicketCommands(commands.Cog):
    """Commands for ticket management"""
    
    def __init__(self, bot):
        self.bot = bot
        self.rate_limiter = RateLimiter()
    
    @commands.command(name="ticket")
    async def ticket_command(self, ctx, action: str = None, *, args: str = None):
        """Main ticket command with subcommands"""
        if action is None:
            await self.show_help(ctx)
        elif action.lower() == "create":
            await self.create_ticket(ctx)
        elif action.lower() == "status":
            await self.check_status(ctx, args)
        elif action.lower() == "history":
            await self.show_history(ctx)
        else:
            await ctx.send("❌ Unknown ticket action. Use `!ticket` for help.")
    
    async def show_help(self, ctx):
        """Show ticket command help"""
        embed = discord.Embed(
            title="🎫 MC-Ticketer Commands",
            description="Automated Minecraft support ticket system",
            color=discord.Color.blue()
        )
        
        embed.add_field(
            name="📝 Create Ticket",
            value="`!ticket create` - Start creating a new support ticket",
            inline=False
        )
        
        embed.add_field(
            name="📊 Check Status",
            value="`!ticket status <id>` - Check the status of a ticket",
            inline=False
        )
        
        embed.add_field(
            name="📋 View History",
            value="`!ticket history` - View your ticket submission history",
            inline=False
        )
        
        embed.set_footer(text="Use commands responsibly and respect rate limits")
        await ctx.send(embed=embed)
    
    async def create_ticket(self, ctx):
        """Start the ticket creation process"""
        # Check rate limiting
        if not await self.rate_limiter.check_user_limit(ctx.author.id):
            await ctx.send("⏰ You've reached your daily ticket limit. Please try again tomorrow.")
            return
        
        # Create and send the ticket modal
        embed = discord.Embed(
            title="🎫 Create Minecraft Support Ticket",
            description="Click the button below to start creating your support ticket.",
            color=discord.Color.green()
        )
        
        view = TicketCreationView(self.bot)
        await ctx.send(embed=embed, view=view)
    
    async def check_status(self, ctx, ticket_id: str):
        """Check the status of a ticket"""
        if not ticket_id:
            await ctx.send("❌ Please provide a ticket ID. Usage: `!ticket status <id>`")
            return
        
        try:
            ticket = await self.bot.database.get_ticket(ticket_id)
            if not ticket:
                await ctx.send("❌ Ticket not found.")
                return
            
            # Check if user owns the ticket or is admin
            if ticket.user_id != ctx.author.id and not await self.is_admin(ctx.author):
                await ctx.send("❌ You can only check your own tickets.")
                return
            
            embed = discord.Embed(
                title=f"🎫 Ticket Status: {ticket_id}",
                color=self.get_status_color(ticket.status)
            )
            
            embed.add_field(name="Status", value=ticket.status.value, inline=True)
            embed.add_field(name="Created", value=ticket.created_at.strftime("%Y-%m-%d %H:%M"), inline=True)
            embed.add_field(name="Type", value=ticket.ticket_type, inline=True)
            embed.add_field(name="Description", value=ticket.description[:100] + "..." if len(ticket.description) > 100 else ticket.description, inline=False)
            
            await ctx.send(embed=embed)
            
        except Exception as e:
            logger.error(f"Error checking ticket status: {e}")
            await ctx.send("❌ Error retrieving ticket information.")
    
    async def show_history(self, ctx):
        """Show user's ticket history"""
        try:
            tickets = await self.bot.database.get_user_tickets(ctx.author.id)
            
            if not tickets:
                await ctx.send("📋 You haven't submitted any tickets yet.")
                return
            
            embed = discord.Embed(
                title="📋 Your Ticket History",
                color=discord.Color.blue()
            )
            
            for ticket in tickets[-10:]:  # Show last 10 tickets
                status_emoji = self.get_status_emoji(ticket.status)
                embed.add_field(
                    name=f"{status_emoji} {ticket.ticket_id}",
                    value=f"**Type:** {ticket.ticket_type}\n**Created:** {ticket.created_at.strftime('%Y-%m-%d')}",
                    inline=True
                )
            
            await ctx.send(embed=embed)
            
        except Exception as e:
            logger.error(f"Error showing ticket history: {e}")
            await ctx.send("❌ Error retrieving ticket history.")
    
    async def is_admin(self, user):
        """Check if user has admin permissions"""
        if not hasattr(user, 'roles'):
            return False
        
        # Check for admin role
        if self.bot.settings.admin_role_id:
            return any(role.id == self.bot.settings.admin_role_id for role in user.roles)
        
        # Fallback to Discord permissions
        return user.guild_permissions.administrator
    
    def get_status_color(self, status: TicketStatus):
        """Get color for ticket status"""
        colors = {
            TicketStatus.PENDING: discord.Color.yellow(),
            TicketStatus.SUBMITTED: discord.Color.blue(),
            TicketStatus.COMPLETED: discord.Color.green(),
            TicketStatus.FAILED: discord.Color.red()
        }
        return colors.get(status, discord.Color.grey())
    
    def get_status_emoji(self, status: TicketStatus):
        """Get emoji for ticket status"""
        emojis = {
            TicketStatus.PENDING: "⏳",
            TicketStatus.SUBMITTED: "📤",
            TicketStatus.COMPLETED: "✅",
            TicketStatus.FAILED: "❌"
        }
        return emojis.get(status, "❓")


class TicketCreationView(discord.ui.View):
    """View for ticket creation button"""
    
    def __init__(self, bot):
        super().__init__(timeout=300)  # 5 minute timeout
        self.bot = bot
    
    @discord.ui.button(label="Create Ticket", style=discord.ButtonStyle.primary, emoji="🎫")
    async def create_ticket_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Handle ticket creation button click"""
        modal = TicketModal(self.bot)
        await interaction.response.send_modal(modal)
