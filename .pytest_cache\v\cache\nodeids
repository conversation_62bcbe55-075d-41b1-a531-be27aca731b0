["tests/test_database.py::test_create_ticket", "tests/test_database.py::test_get_statistics", "tests/test_database.py::test_get_ticket", "tests/test_database.py::test_get_user_tickets", "tests/test_database.py::test_update_ticket_status", "tests/test_error_handler.py::test_classify_network_error", "tests/test_error_handler.py::test_classify_unknown_error", "tests/test_error_handler.py::test_classify_validation_error", "tests/test_error_handler.py::test_handle_error_with_ticket_id", "tests/test_error_handler.py::test_handle_exception_auto_classify", "tests/test_integration.py::test_concurrent_ticket_creation", "tests/test_integration.py::test_database_statistics_integration", "tests/test_integration.py::test_error_handling_integration", "tests/test_integration.py::test_full_ticket_workflow", "tests/test_integration.py::test_rate_limiting_integration", "tests/test_integration.py::test_scraper_initialization", "tests/test_integration.py::test_user_summary_integration", "tests/test_rate_limiter.py::test_daily_reset", "tests/test_rate_limiter.py::test_multiple_users", "tests/test_rate_limiter.py::test_user_exceeds_limits", "tests/test_rate_limiter.py::test_user_within_limits"]