#!/usr/bin/env python3
"""
Test script to verify Minecraft support URL accessibility
"""
import asyncio
import sys
from playwright.async_api import async_playwright


async def test_minecraft_url():
    """Test if we can access the Minecraft support URL"""
    print("🧪 Testing Minecraft Support URL Access")
    print("=" * 50)
    
    # Try multiple URLs to find the correct one
    test_urls = [
        "https://help.minecraft.net/hc/en-us/requests/new",
        "https://help.minecraft.net/hc/en-us/request/new",
        "https://help.minecraft.net/hc/en-us",
        "https://help.minecraft.net/",
        "https://www.minecraft.net/en-us/help"
    ]
    
    async with async_playwright() as p:
        try:
            # Launch browser
            print("🚀 Launching browser...")
            browser = await p.chromium.launch(
                headless=False,  # Show browser for debugging
                args=[
                    '--no-sandbox',
                    '--disable-http2',
                    '--disable-dev-shm-usage',
                    '--disable-gpu'
                ]
            )
            
            # Create page
            context = await browser.new_context(
                user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            )
            page = await context.new_page()
            
            # Try each URL until we find one that works
            working_url = None
            for test_url in test_urls:
                print(f"🌐 Trying: {test_url}")

                try:
                    await page.goto(test_url, timeout=15000)
                    title = await page.title()
                    current_url = page.url

                    print(f"✅ Successfully navigated to: {test_url}")
                    print(f"📄 Page title: {title}")
                    print(f"🔗 Current URL: {current_url}")

                    # Check for form elements or contact links
                    print("🔍 Looking for support elements...")

                    support_selectors = [
                        'form',
                        'input[type="email"]',
                        'textarea',
                        'input[type="text"]',
                        'select',
                        'a[href*="contact"]',
                        'a[href*="support"]',
                        'a[href*="request"]',
                        'button:has-text("Contact")',
                        'button:has-text("Support")'
                    ]

                    found_elements = []
                    for selector in support_selectors:
                        try:
                            elements = await page.query_selector_all(selector)
                            if elements:
                                found_elements.append(f"{selector}: {len(elements)} found")
                        except:
                            pass

                    if found_elements:
                        print("✅ Support elements found:")
                        for element in found_elements:
                            print(f"  - {element}")
                        working_url = test_url

                        # Take a screenshot for debugging
                        screenshot_name = f"minecraft_page_{test_url.replace('https://', '').replace('/', '_')}.png"
                        await page.screenshot(path=screenshot_name)
                        print(f"📸 Screenshot saved as '{screenshot_name}'")
                        break
                    else:
                        print("❌ No support elements found")

                except Exception as e:
                    print(f"❌ Failed to navigate to {test_url}: {e}")
                    continue

            if working_url:
                print(f"\n🎉 Found working URL: {working_url}")

                # Look for contact/support links
                print("🔗 Looking for contact/support links...")
                contact_links = await page.query_selector_all('a[href*="contact"], a[href*="support"], a[href*="request"]')

                for i, link in enumerate(contact_links[:5]):  # Check first 5 links
                    try:
                        href = await link.get_attribute('href')
                        text = await link.inner_text()
                        print(f"  Link {i+1}: {text.strip()} -> {href}")
                    except:
                        pass

                print("\n⏳ Waiting 3 seconds to observe the page...")
                await asyncio.sleep(3)
            else:
                print("❌ No working URLs found")
            
            await browser.close()
            
        except Exception as e:
            print(f"❌ Browser setup failed: {e}")
            return False
    
    return True


if __name__ == "__main__":
    try:
        asyncio.run(test_minecraft_url())
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
    except Exception as e:
        print(f"❌ Test failed: {e}")
        sys.exit(1)
