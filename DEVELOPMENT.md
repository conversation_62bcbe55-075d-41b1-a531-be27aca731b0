# MC-Ticketer Development Guide

## Architecture Overview

MC-Ticketer is built with a modular architecture consisting of several key components:

### Core Components

1. **Discord Bot (`src/bot/`)**
   - `client.py`: Main bot client with event handling
   - `cogs/`: Command modules for different functionalities
   - `views/`: Discord UI components (modals, buttons)

2. **Web Scraper (`src/scraper/`)**
   - `minecraft_scraper.py`: Main scraping logic for Minecraft support
   - `anti_bot.py`: Anti-detection and stealth measures

3. **Database (`src/database/`)**
   - `models.py`: Data models and database operations
   - SQLite database for ticket storage and user management

4. **Utilities (`src/utils/`)**
   - `logger.py`: Logging configuration
   - `rate_limiter.py`: User rate limiting
   - `error_handler.py`: Centralized error handling

## Development Setup

### Prerequisites

- Python 3.9+
- Discord Bot Token
- Git

### Installation

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd MC-Ticketer
   ```

2. Create virtual environment:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install dependencies:
   ```bash
   pip install -r requirements.txt
   playwright install
   ```

4. Setup environment:
   ```bash
   python setup.py
   ```

5. Configure `.env` file with your settings

## Testing

### Running Tests

```bash
# Run all tests
python -m pytest tests/

# Run specific test file
python -m pytest tests/test_database.py

# Run with coverage
python -m pytest tests/ --cov=src/

# Run with verbose output
python -m pytest tests/ -v
```

### Test Structure

- `test_database.py`: Database functionality tests
- `test_rate_limiter.py`: Rate limiting tests
- `test_error_handler.py`: Error handling tests

## Code Style and Standards

### Python Style Guide

- Follow PEP 8 style guidelines
- Use type hints where possible
- Document functions and classes with docstrings
- Use meaningful variable and function names

### Error Handling

- Use the centralized `ErrorHandler` class
- Log errors with appropriate context
- Provide user-friendly error messages
- Update ticket status on failures

### Logging

- Use the configured logger from `src/utils/logger.py`
- Log at appropriate levels (DEBUG, INFO, WARNING, ERROR)
- Include context information in log messages

## Adding New Features

### Adding Discord Commands

1. Create a new cog in `src/bot/cogs/`
2. Inherit from `commands.Cog`
3. Add commands using `@commands.command()` decorator
4. Register the cog in `client.py`

Example:
```python
class NewFeatureCog(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
    
    @commands.command(name="newcommand")
    async def new_command(self, ctx):
        await ctx.send("Hello from new command!")
```

### Adding Database Models

1. Define new models in `src/database/models.py`
2. Use Pydantic for data validation
3. Add database operations as methods in the `Database` class
4. Create migration scripts if needed

### Adding Web Scraping Features

1. Extend `MinecraftScraper` class
2. Use anti-bot measures from `AntiBot` class
3. Handle errors appropriately
4. Add stealth techniques for detection avoidance

## Configuration

### Environment Variables

All configuration is managed through environment variables:

- `DISCORD_BOT_TOKEN`: Discord bot token
- `COMMAND_PREFIX`: Bot command prefix (default: !)
- `MAX_TICKETS_PER_USER_PER_DAY`: Rate limiting
- `HEADLESS_MODE`: Browser headless mode
- `LOG_LEVEL`: Logging level

### Adding New Configuration

1. Add to `src/config.py` Settings class
2. Add to `.env.example`
3. Document in README.md

## Deployment

### Production Considerations

1. **Security**:
   - Keep bot token secure
   - Use environment variables for sensitive data
   - Implement proper permission checks

2. **Performance**:
   - Monitor memory usage
   - Implement connection pooling if needed
   - Use appropriate timeouts

3. **Reliability**:
   - Implement proper error handling
   - Add health checks
   - Monitor logs for issues

### Docker Deployment

Create a `Dockerfile`:
```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
RUN playwright install

COPY . .
CMD ["python", "main.py"]
```

## Troubleshooting

### Common Issues

1. **Bot not responding**:
   - Check bot token
   - Verify bot permissions
   - Check logs for errors

2. **Web scraping failures**:
   - Check if target site changed
   - Verify anti-bot measures
   - Check network connectivity

3. **Database errors**:
   - Check file permissions
   - Verify database path
   - Check disk space

### Debug Mode

Enable debug logging by setting `LOG_LEVEL=DEBUG` in `.env`

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

### Code Review Checklist

- [ ] Code follows style guidelines
- [ ] Tests are included and passing
- [ ] Documentation is updated
- [ ] Error handling is implemented
- [ ] Logging is appropriate
- [ ] Security considerations addressed

## API Reference

### Database Models

- `TicketData`: Represents a support ticket
- `TicketStatus`: Enum for ticket statuses
- `Database`: Main database interface

### Error Types

- `ErrorType`: Enum for error categorization
- `ErrorHandler`: Centralized error handling

### Rate Limiting

- `RateLimiter`: User rate limiting functionality

## Security Considerations

1. **Bot Token Security**:
   - Never commit tokens to version control
   - Use environment variables
   - Rotate tokens regularly

2. **User Data Protection**:
   - Encrypt sensitive data
   - Implement data retention policies
   - Follow privacy regulations

3. **Web Scraping Ethics**:
   - Respect robots.txt
   - Implement rate limiting
   - Don't overload target servers

## Performance Optimization

1. **Database**:
   - Use indexes for frequently queried fields
   - Implement connection pooling
   - Regular database maintenance

2. **Web Scraping**:
   - Reuse browser instances
   - Implement caching where appropriate
   - Use efficient selectors

3. **Discord Bot**:
   - Use async/await properly
   - Implement command cooldowns
   - Monitor memory usage
