"""
Admin-only Discord commands
"""
import discord
from discord.ext import commands
from loguru import logger
from src.config import settings


class AdminCommands(commands.Cog):
    """Administrative commands for bot management"""
    
    def __init__(self, bot):
        self.bot = bot
    
    def cog_check(self, ctx):
        """Check if user has admin permissions for all commands in this cog"""
        return self.is_admin(ctx.author)
    
    @commands.command(name="admin")
    async def admin_command(self, ctx, action: str = None, *, args: str = None):
        """Main admin command with subcommands"""
        if action is None:
            await self.show_admin_help(ctx)
        elif action.lower() == "stats":
            await self.show_stats(ctx)
        elif action.lower() == "users":
            await self.manage_users(ctx, args)
        elif action.lower() == "logs":
            await self.show_logs(ctx)
        elif action.lower() == "config":
            await self.show_config(ctx)
        else:
            await ctx.send("❌ Unknown admin action. Use `!admin` for help.")
    
    async def show_admin_help(self, ctx):
        """Show admin command help"""
        embed = discord.Embed(
            title="🛠️ Admin Commands",
            description="Administrative commands for MC-Ticketer",
            color=discord.Color.red()
        )
        
        embed.add_field(
            name="📊 Statistics",
            value="`!admin stats` - View system statistics",
            inline=False
        )
        
        embed.add_field(
            name="👥 User Management",
            value="`!admin users` - View user information",
            inline=False
        )
        
        embed.add_field(
            name="📝 Logs",
            value="`!admin logs` - View recent system logs",
            inline=False
        )
        
        embed.add_field(
            name="⚙️ Configuration",
            value="`!admin config` - View current configuration",
            inline=False
        )
        
        await ctx.send(embed=embed)
    
    async def show_stats(self, ctx):
        """Show system statistics"""
        try:
            stats = await self.bot.database.get_statistics()
            
            embed = discord.Embed(
                title="📊 System Statistics",
                color=discord.Color.blue()
            )
            
            embed.add_field(
                name="Total Tickets",
                value=str(stats.get('total_tickets', 0)),
                inline=True
            )
            
            embed.add_field(
                name="Successful Submissions",
                value=str(stats.get('successful_tickets', 0)),
                inline=True
            )
            
            embed.add_field(
                name="Failed Submissions",
                value=str(stats.get('failed_tickets', 0)),
                inline=True
            )
            
            embed.add_field(
                name="Active Users",
                value=str(stats.get('active_users', 0)),
                inline=True
            )
            
            embed.add_field(
                name="Today's Tickets",
                value=str(stats.get('todays_tickets', 0)),
                inline=True
            )
            
            success_rate = 0
            if stats.get('total_tickets', 0) > 0:
                success_rate = (stats.get('successful_tickets', 0) / stats.get('total_tickets', 0)) * 100
            
            embed.add_field(
                name="Success Rate",
                value=f"{success_rate:.1f}%",
                inline=True
            )
            
            await ctx.send(embed=embed)
            
        except Exception as e:
            logger.error(f"Error getting statistics: {e}")
            await ctx.send("❌ Error retrieving statistics.")
    
    async def manage_users(self, ctx, args):
        """Manage users and permissions"""
        try:
            users = await self.bot.database.get_user_summary()
            
            embed = discord.Embed(
                title="👥 User Summary",
                color=discord.Color.green()
            )
            
            for user_data in users[:10]:  # Show top 10 users
                user_id = user_data['user_id']
                ticket_count = user_data['ticket_count']
                last_ticket = user_data['last_ticket']
                
                try:
                    user = await self.bot.fetch_user(user_id)
                    username = user.display_name
                except:
                    username = f"Unknown User ({user_id})"
                
                embed.add_field(
                    name=username,
                    value=f"Tickets: {ticket_count}\nLast: {last_ticket}",
                    inline=True
                )
            
            await ctx.send(embed=embed)
            
        except Exception as e:
            logger.error(f"Error managing users: {e}")
            await ctx.send("❌ Error retrieving user information.")
    
    async def show_logs(self, ctx):
        """Show recent system logs"""
        try:
            # Read last 20 lines from log file
            log_file = settings.log_file
            with open(log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                recent_logs = lines[-20:] if len(lines) > 20 else lines
            
            log_content = ''.join(recent_logs)
            
            # Truncate if too long for Discord
            if len(log_content) > 1900:
                log_content = log_content[-1900:] + "\n... (truncated)"
            
            embed = discord.Embed(
                title="📝 Recent Logs",
                description=f"```\n{log_content}\n```",
                color=discord.Color.orange()
            )
            
            await ctx.send(embed=embed)
            
        except Exception as e:
            logger.error(f"Error reading logs: {e}")
            await ctx.send("❌ Error reading log file.")
    
    async def show_config(self, ctx):
        """Show current configuration (sanitized)"""
        embed = discord.Embed(
            title="⚙️ Current Configuration",
            color=discord.Color.purple()
        )
        
        embed.add_field(
            name="Command Prefix",
            value=settings.command_prefix,
            inline=True
        )
        
        embed.add_field(
            name="Max Tickets/Day",
            value=str(settings.max_tickets_per_user_per_day),
            inline=True
        )
        
        embed.add_field(
            name="Rate Limit Window",
            value=f"{settings.rate_limit_window_minutes} minutes",
            inline=True
        )
        
        embed.add_field(
            name="Log Level",
            value=settings.log_level,
            inline=True
        )
        
        embed.add_field(
            name="Headless Mode",
            value="Enabled" if settings.headless_mode else "Disabled",
            inline=True
        )
        
        embed.add_field(
            name="Browser Timeout",
            value=f"{settings.browser_timeout}ms",
            inline=True
        )
        
        await ctx.send(embed=embed)
    
    def is_admin(self, user):
        """Check if user has admin permissions"""
        if not hasattr(user, 'roles'):
            return False
        
        # Check for admin role
        if settings.admin_role_id:
            return any(role.id == settings.admin_role_id for role in user.roles)
        
        # Fallback to Discord permissions
        return user.guild_permissions.administrator
