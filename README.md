# MC-Ticketer

An automated Minecraft support ticket generator system with Discord bot interface and web scraping capabilities.

## Overview

MC-Ticketer is designed to automate the process of submitting support tickets to Minecraft's help center through a Discord bot interface. The system uses web scraping techniques to interact with Minecraft's support system and provides users with a convenient way to submit tickets directly from <PERSON>rd.

## Features

- **Discord Bot Interface**: Easy-to-use commands for ticket submission
- **Web Scraping Automation**: Automated interaction with Minecraft support forms
- **User Management**: Permission-based access and rate limiting
- **Ticket Tracking**: Database storage of ticket history and status
- **Error Handling**: Comprehensive error handling and user feedback
- **Anti-Bot Countermeasures**: Strategies to handle CAPTCHAs and rate limiting

## Project Structure

```
MC-Ticketer/
├── src/
│   ├── bot/                 # Discord bot implementation
│   ├── scraper/            # Web scraping modules
│   ├── database/           # Database models and operations
│   ├── utils/              # Utility functions and helpers
│   └── config.py           # Configuration management
├── data/                   # Database files
├── logs/                   # Log files
├── main.py                 # Application entry point
├── requirements.txt        # Python dependencies
└── .env.example           # Environment variables template
```

## Technology Stack

- **Python 3.9+**: Main programming language
- **discord.py**: Discord bot framework
- **Playwright**: Modern web scraping and browser automation
- **SQLite**: Local database for ticket storage
- **Pydantic**: Data validation and settings management
- **Loguru**: Enhanced logging capabilities

## Installation

### Prerequisites

- Python 3.9 or higher
- Discord Bot Token (see Discord Developer Portal)
- Git

### Setup Steps

1. **Clone the repository**:
   ```bash
   git clone https://github.com/yourusername/MC-Ticketer.git
   cd MC-Ticketer
   ```

2. **Create virtual environment**:
   ```bash
   python -m venv venv
   # On Windows:
   venv\Scripts\activate
   # On macOS/Linux:
   source venv/bin/activate
   ```

3. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

4. **Install Playwright browsers**:
   ```bash
   playwright install
   ```

5. **Configure environment variables**:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

6. **Set up Discord Bot**:
   - Go to [Discord Developer Portal](https://discord.com/developers/applications)
   - Create a new application and bot
   - Copy the bot token to your `.env` file
   - Invite the bot to your server with appropriate permissions

## Configuration

Edit the `.env` file with your specific settings:

```env
# Discord Bot Configuration
DISCORD_BOT_TOKEN=your_discord_bot_token_here
DISCORD_GUILD_ID=your_discord_server_id_here

# Bot Settings
COMMAND_PREFIX=!
ADMIN_ROLE_ID=your_admin_role_id_here

# Rate Limiting
MAX_TICKETS_PER_USER_PER_DAY=3
RATE_LIMIT_WINDOW_MINUTES=60
```

## Usage

### Starting the Bot

```bash
python main.py
```

### Discord Commands

- `!ticket create` - Start the ticket creation process
- `!ticket status <id>` - Check ticket status
- `!ticket history` - View your ticket history
- `!help` - Show available commands

### Admin Commands

- `!admin stats` - View system statistics
- `!admin users` - Manage user permissions
- `!admin logs` - View recent logs

## Development Status

This project is currently in development. The following components are planned:

- [x] Project structure and configuration
- [ ] Discord bot foundation
- [ ] Web scraping module
- [ ] Support form analysis and automation
- [ ] Integration layer
- [ ] Anti-bot countermeasures
- [ ] Error handling and logging
- [ ] Testing and documentation

## Important Notes

⚠️ **Disclaimer**: This tool is for educational and automation purposes. Please ensure compliance with Minecraft's Terms of Service and use responsibly.

⚠️ **Rate Limiting**: The system includes built-in rate limiting to prevent abuse. Respect Minecraft's servers and support system.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For issues and questions:
- Open an issue on GitHub
- Check the logs in the `logs/` directory
- Review the configuration in `.env`

